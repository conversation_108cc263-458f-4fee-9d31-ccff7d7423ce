import { allBuildingsData, getAllBuildingsStats } from "./productionData";

// 导出所有楼栋的详细数据
export const exportAllBuildingsData = () => {
  console.log("=== 1-19栋生产数据详情 ===\n");

  for (let buildingId = 1; buildingId <= 19; buildingId++) {
    const data = allBuildingsData[buildingId];
    console.log(`\n🏢 ${buildingId}#楼 (共${data.length}条数据):`);
    console.log("─".repeat(80));
    console.log("序号 | 厂区    | 车间 | 方位 | 放养规格   | 日龄 | 头/斤");
    console.log("─".repeat(80));

    data.forEach((item, index) => {
      const no = (index + 1).toString().padStart(2, " ");
      const factory = item.factory.padEnd(8, " ");
      const workshop = item.workshop.padEnd(4, " ");
      const direction = item.direction.padEnd(4, " ");
      const spec = item.specification.padEnd(10, " ");
      const dayAge = item.dayAge.toString().padStart(3, " ");
      const count = item.countPerJin.toString().padStart(3, " ");

      console.log(
        `${no}   | ${factory} | ${workshop} | ${direction} | ${spec} | ${dayAge}  | ${count}`
      );
    });
  }

  // 输出统计信息
  console.log("\n\n=== 各楼栋统计信息 ===");
  const stats = getAllBuildingsStats();

  console.log(
    "\n楼栋 | 数据量 | 平均日龄 | 平均头/斤 | 车间数 | 方位数 | 规格数"
  );
  console.log("─".repeat(70));

  for (let buildingId = 1; buildingId <= 19; buildingId++) {
    const stat = stats[buildingId];
    if (stat) {
      const building = `${buildingId}#楼`.padEnd(6, " ");
      const total = stat.totalItems.toString().padStart(4, " ");
      const avgAge = stat.avgDayAge.toString().padStart(6, " ");
      const avgCount = stat.avgCountPerJin.toString().padStart(7, " ");
      const workshops = stat.workshops.length.toString().padStart(4, " ");
      const directions = stat.directions.length.toString().padStart(4, " ");
      const specs = stat.specifications.length.toString().padStart(4, " ");

      console.log(
        `${building} | ${total}   | ${avgAge}     | ${avgCount}     | ${workshops}    | ${directions}    | ${specs}`
      );
    }
  }

  // 总体统计
  const totalItems = Object.values(allBuildingsData).reduce(
    (sum, data) => sum + data.length,
    0
  );
  const allItems = Object.values(allBuildingsData).flat();
  const totalAvgAge = Math.round(
    allItems.reduce((sum, item) => sum + item.dayAge, 0) / allItems.length
  );
  const totalAvgCount = Math.round(
    allItems.reduce((sum, item) => sum + item.countPerJin, 0) / allItems.length
  );

  console.log("─".repeat(70));
  console.log(
    `总计   | ${totalItems}   | ${totalAvgAge}     | ${totalAvgCount}     | 6    | 4    | 4`
  );

  return {
    totalBuildings: 19,
    totalItems,
    averageDayAge: totalAvgAge,
    averageCountPerJin: totalAvgCount,
    buildingsData: allBuildingsData,
    buildingsStats: stats,
  };
};

// 导出JSON格式数据
export const exportBuildingsDataAsJSON = () => {
  return {
    metadata: {
      totalBuildings: 19,
      generatedAt: new Date().toISOString(),
      description: "1-19栋水产养殖生产数据",
    },
    buildings: allBuildingsData,
    statistics: getAllBuildingsStats(),
  };
};

// 导出CSV格式数据
export const exportBuildingsDataAsCSV = () => {
  const headers = ["楼栋", "车间", "方位", "放养规格", "日龄", "头/斤"];
  const csvData = [headers.join(",")];

  for (let buildingId = 1; buildingId <= 19; buildingId++) {
    const data = allBuildingsData[buildingId];
    data.forEach((item) => {
      const row = [
        item.factory,
        item.workshop,
        item.direction,
        item.specification,
        item.dayAge,
        item.countPerJin,
      ];
      csvData.push(row.join(","));
    });
  }

  return csvData.join("\n");
};
