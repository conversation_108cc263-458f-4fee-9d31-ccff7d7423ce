export interface ProductionItem {
  factory: string;
  workshop: string;
  direction: string;
  specification: string;
  dayAge: number;
  countPerJin: number;
}

// 生成1-19栋的生产数据
export const generateBuildingData = (buildingId: number): ProductionItem[] => {
  const workshops = ["1", "2", "3", "4", "5", "6"];
  const directions = ["A区", "B区", "C区", "D区"];
  const specifications = ["2到3公分", "3到4公分", "4到5公分", "5到6公分"];
  
  const data: ProductionItem[] = [];
  
  // 为每个楼栋生成20-30条数据
  const itemCount = 20 + Math.floor(Math.random() * 10);
  
  // 设置随机种子，确保相同楼栋生成相同数据
  const seed = buildingId * 1000;
  let randomSeed = seed;
  
  const seededRandom = () => {
    randomSeed = (randomSeed * 9301 + 49297) % 233280;
    return randomSeed / 233280;
  };
  
  for (let i = 0; i < itemCount; i++) {
    data.push({
      factory: `${buildingId}#楼`,
      workshop: workshops[Math.floor(seededRandom() * workshops.length)],
      direction: directions[Math.floor(seededRandom() * directions.length)],
      specification: specifications[Math.floor(seededRandom() * specifications.length)],
      dayAge: 30 + Math.floor(seededRandom() * 60), // 30-90天
      countPerJin: 80 + Math.floor(seededRandom() * 120), // 80-200头/斤
    });
  }
  
  return data;
};

// 预生成1-19栋的所有数据
export const allBuildingsData: Record<number, ProductionItem[]> = {};

// 初始化所有楼栋数据
for (let buildingId = 1; buildingId <= 19; buildingId++) {
  allBuildingsData[buildingId] = generateBuildingData(buildingId);
}

// 获取指定楼栋的数据
export const getBuildingData = (buildingId: number): ProductionItem[] => {
  return allBuildingsData[buildingId] || [];
};

// 获取所有楼栋数据
export const getAllBuildingsData = (): Record<number, ProductionItem[]> => {
  return allBuildingsData;
};

// 楼栋统计信息
export const getBuildingStats = (buildingId: number) => {
  const data = getBuildingData(buildingId);
  if (!data.length) return null;
  
  return {
    totalItems: data.length,
    avgDayAge: Math.round(data.reduce((sum, item) => sum + item.dayAge, 0) / data.length),
    avgCountPerJin: Math.round(data.reduce((sum, item) => sum + item.countPerJin, 0) / data.length),
    workshops: [...new Set(data.map(item => item.workshop))].sort(),
    directions: [...new Set(data.map(item => item.direction))].sort(),
    specifications: [...new Set(data.map(item => item.specification))].sort(),
  };
};

// 所有楼栋的统计信息
export const getAllBuildingsStats = () => {
  const stats: Record<number, any> = {};
  for (let buildingId = 1; buildingId <= 19; buildingId++) {
    stats[buildingId] = getBuildingStats(buildingId);
  }
  return stats;
};
