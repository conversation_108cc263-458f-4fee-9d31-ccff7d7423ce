<template>
  <EChartsComponent :option="chartOption" />
</template>

<script setup lang="ts">
import { computed, watch } from "vue";
import EChartsComponent from "./EChartsComponent.vue";
import * as echarts from "echarts";

interface Props {
  buildingId: number;
}

const props = defineProps<Props>();

// 生成虾苗生长数据
const generateGrowthData = (buildingId: number) => {
  const days = [];
  const weights = [];
  const lengths = [];

  // 根据楼栋ID生成不同的基础数据
  const baseOffset = (buildingId - 1) * 0.1;

  // 生成30天的数据
  for (let i = 1; i <= 30; i++) {
    days.push(`第${i}天`);

    // 体重增长曲线 (克)
    const weight = (
      0.1 +
      i * 0.15 +
      Math.sin(i * 0.2) * 0.05 +
      baseOffset
    ).toFixed(2);
    weights.push(Number(weight));

    // 体长增长曲线 (厘米)
    const length = (
      1.0 +
      i * 0.08 +
      Math.sin(i * 0.3) * 0.02 +
      baseOffset * 0.5
    ).toFixed(2);
    lengths.push(Number(length));
  }

  return { days, weights, lengths };
};

// 图表选项的计算属性
const chartOption = computed(() => {
  const data = generateGrowthData(props.buildingId);
  return createChartOption(data);
});

// 创建图表配置
const createChartOption = (
  data: ReturnType<typeof generateGrowthData>
): echarts.EChartsOption => {
  return {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 34, 68, 0.9)",
      borderColor: "#66ccff",
      textStyle: { color: "#fff" },
      formatter: (params: any) => {
        let result = `${params[0].name}<br/>`;
        params.forEach((param: any) => {
          const unit = param.seriesName === "平均体重" ? "g" : "cm";
          result += `${param.marker}${param.seriesName}: ${param.value}${unit}<br/>`;
        });
        return result;
      },
    },
    legend: {
      data: ["平均体重", "平均体长"],
      textStyle: { color: "#ccc" },
      top: "5%",
    },
    grid: {
      left: "10%",
      right: "10%",
      top: "20%",
      bottom: "15%",
    },
    xAxis: {
      type: "category",
      data: data.days,
      axisLine: {
        lineStyle: { color: "#0efcff", width: 1, type: "solid" },
      },
      axisLabel: {
        color: "#ccc",
        fontSize: 10,
        interval: 4,
      },
    },
    yAxis: [
      {
        type: "value",
        name: "体重(g)",
        nameTextStyle: { color: "#ccc" },
        position: "left",
        axisLine: {
          lineStyle: { color: "#333" },
        },
        axisLabel: {
          color: "#ccc",
          fontSize: 10,
          formatter: "{value}g",
        },
        splitLine: {
          lineStyle: { color: "#0efcff", type: "dashed" },
        },
      },
      {
        type: "value",
        name: "体长(cm)",
        nameTextStyle: { color: "#ccc" },
        position: "right",
        axisLine: {
          lineStyle: { color: "#333" },
        },
        axisLabel: {
          color: "#ccc",
          fontSize: 10,
          formatter: "{value}cm",
        },
        splitLine: { show: false },
      },
    ],
    series: [
      {
        name: "平均体重",
        type: "line",
        yAxisIndex: 0,
        data: data.weights,
        smooth: true,
        lineStyle: {
          color: "#4fc3f7",
          width: 3,
        },
        itemStyle: {
          color: "#4fc3f7",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: "#4fc3f740" },
              { offset: 1, color: "#4fc3f710" },
            ],
          },
        },
        symbol: "circle",
        symbolSize: 6,
      },
      {
        name: "平均体长",
        type: "line",
        yAxisIndex: 1,
        data: data.lengths,
        smooth: true,
        lineStyle: {
          color: "#29b6f6",
          width: 3,
        },
        itemStyle: {
          color: "#29b6f6",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: "#29b6f640" },
              { offset: 1, color: "#29b6f610" },
            ],
          },
        },
        symbol: "diamond",
        symbolSize: 6,
      },
    ],
  };
};

// 监听楼栋变化，计算属性会自动重新计算
watch(
  () => props.buildingId,
  () => {
    // 计算属性会自动重新计算
  },
  { immediate: false }
);
</script>
