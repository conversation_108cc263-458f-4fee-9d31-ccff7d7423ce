<template>
  <div class="aquaculture-dashboard">
    <!-- 顶部标题栏 -->
    <div class="header-section">
      <div class="header-left">
        <span class="seed-date">{{ seedDateText }}</span>
      </div>
      <div class="header-center">
        <h1 class="main-title">银海集团1号楼第一层数据检测信息系统</h1>
      </div>
      <div class="header-right">
        <!-- 预留右侧空间 -->
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧区域 -->
      <div class="left-section">
        <!-- 环境监测 -->
        <div class="panel">
          <h3 class="panel-title">环境监测</h3>
          <div class="monitor-grid">
            <div
              class="monitor-item"
              v-for="item in environmentData"
              :key="item.name"
            >
              <span class="monitor-label">{{ item.name }}：</span>
              <span class="monitor-value">{{ item.value }}</span>
              <span class="monitor-unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>

        <!-- 饲料投放与产品重量 -->
        <div class="panel chart-panel">
          <h3 class="panel-title">
            {{ currentPoolId }}号池子 - 饲料投放与产品重量
          </h3>
          <div class="chart-container">
            <EChartsComponent :option="feedAndWeightOption" />
          </div>
        </div>

        <!-- 视频监控 -->
        <div class="panel h-0 flex-1">
          <h3 class="panel-title">视频监控</h3>
          <div class="video-content">
            <div class="main-video">
              <img
                :src="mainVideoPlaceholder"
                alt="主监控"
                class="video-placeholder"
              />
            </div>
            <div class="sub-videos">
              <div class="sub-video" v-for="i in 3" :key="i">
                <img
                  :src="subVideoPlaceholder"
                  alt="监控"
                  class="video-placeholder"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间区域 -->
      <div class="center-section">
        <!-- 池子显示区域 -->
        <div class="panel pools-display">
          <h3 class="panel-title">养殖池检测</h3>
          <div class="pools-grid">
            <div
              v-for="pool in pools"
              :key="pool.id"
              class="pool-item"
              :class="{ active: pool.id === currentPoolId }"
              @click="selectPool(pool.id, true)"
            >
              <div class="pool-number">{{ pool.id }}</div>
              <div class="pool-status" :class="pool.status">
                {{ pool.statusText }}
              </div>
            </div>
          </div>
        </div>

        <!-- 报警信息 -->
        <div class="panel alarm-info">
          <h3 class="panel-title">报警信息</h3>
          <div class="alarm-table-wrapper">
            <AlarmTable />
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-section">
        <!-- 液位监测 -->
        <div class="panel chart-panel">
          <h3 class="panel-title">液位监测</h3>
          <div class="chart-container">
            <EChartsComponent :option="liquidLevelOption" />
          </div>
        </div>

        <!-- 水温监测 -->
        <div class="panel chart-panel">
          <h3 class="panel-title">水温监测</h3>
          <div class="chart-container">
            <EChartsComponent :option="temperatureOption" />
          </div>
        </div>

        <!-- pH值监测 -->
        <div class="panel chart-panel">
          <h3 class="panel-title">pH值监测</h3>
          <div class="chart-container">
            <EChartsComponent :option="phOption" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from "vue";
import EChartsComponent from "../components/EChartsComponent.vue";
import AlarmTable from "../components/AlarmTable.vue";
import type { EChartsOption } from "echarts";
import dayjs from "dayjs";

// Tooltip参数类型定义
interface TooltipParam {
  axisValue?: string;
  value: number;
  seriesName?: string;
  marker?: string;
}

// 投苗日期计算
const seedDate = "2025-07-20"; // 固定的投苗日期
const seedDateText = computed(() => {
  const seedDay = dayjs(seedDate);
  const today = dayjs();
  const daysDiff = today.diff(seedDay, "day");
  return `投苗日期: ${seedDate} (${daysDiff}天)`;
});

// 环境监测数据
const environmentData = ref([
  { name: "温度", value: "28.5", unit: "°C" },
  { name: "水位", value: "1.2", unit: "m" },
  { name: "PH", value: "7.8", unit: "" },
  { name: "溶氧", value: "6.5", unit: "mg/L" },
  { name: "盐度", value: "15.2", unit: "‰" },
]);

// 注释：已移除养殖区轮播相关变量

// 养殖区数据
const areaData = ref<
  Record<number, { small: number; medium: number; large: number }>
>({
  1: { small: 1200, medium: 800, large: 400 },
  2: { small: 1150, medium: 850, large: 450 },
  3: { small: 1300, medium: 780, large: 380 },
  // ... 其他区域数据会动态生成
});

// 注释：池子数据映射已移除，现在使用饲料投放与产品重量图表

// 饲料投放与产品重量数据
const feedAndWeightData = {
  categories: ["1月", "2月", "3月", "4月", "5月", "6月"],
  feedData: [120, 135, 142, 158, 165, 172], // 饲料投放量(kg)
  weightData: [850, 920, 980, 1050, 1120, 1200], // 产品重量(kg)
};

// 饲料投放与产品重量柱状图配置
const feedAndWeightOption = computed<EChartsOption>(() => {
  return {
    backgroundColor: "transparent",
    grid: { left: 40, right: 50, top: 30, bottom: 20 },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#0efcff",
      borderWidth: 1,
      textStyle: {
        color: "#0efcff",
        fontSize: 12,
      },
      formatter: (params: unknown) => {
        const paramArray = Array.isArray(params) ? params : [params];
        const feedParam = paramArray.find(
          (p: TooltipParam & { seriesName?: string }) =>
            p.seriesName === "饲料投放"
        );
        const weightParam = paramArray.find(
          (p: TooltipParam & { seriesName?: string }) =>
            p.seriesName === "产品重量"
        );
        return `${feedParam?.axisValue}<br/>
                饲料投放: ${feedParam?.value}kg<br/>
                产品重量: ${weightParam?.value}kg`;
      },
    },
    legend: {
      data: ["饲料投放", "产品重量"],
      textStyle: { color: "#0efcff" },
      top: "5%",
    },
    xAxis: {
      type: "category",
      data: feedAndWeightData.categories,
      axisLine: { lineStyle: { color: "#0efcff" } },
      axisLabel: {
        color: "#0efcff",
        fontSize: 10,
        interval: 0,
        margin: 8,
      },
    },
    yAxis: [
      {
        type: "value",
        name: "饲料(kg)",
        nameTextStyle: { color: "#00ff88" },
        axisLine: { lineStyle: { color: "#0efcff" } },
        axisLabel: { color: "#0efcff", fontSize: 10 },
        splitLine: { lineStyle: { color: "rgba(14, 252, 255, 0.2)" } },
      },
      {
        type: "value",
        name: "重量(kg)",
        nameTextStyle: { color: "#ff6b6b" },
        axisLine: { lineStyle: { color: "#0efcff" } },
        axisLabel: { color: "#0efcff", fontSize: 10 },
        splitLine: { show: false },
      },
    ],
    series: [
      {
        name: "饲料投放",
        type: "bar",
        yAxisIndex: 0,
        data: feedAndWeightData.feedData,
        itemStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: "#00ff88" },
              { offset: 1, color: "rgba(0, 255, 136, 0.3)" },
            ],
          },
        },
        barWidth: "30%",
      },
      {
        name: "产品重量",
        type: "bar",
        yAxisIndex: 1,
        data: feedAndWeightData.weightData,
        itemStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: "#ff6b6b" },
              { offset: 1, color: "rgba(255, 107, 107, 0.3)" },
            ],
          },
        },
        barWidth: "30%",
      },
    ],
  };
});

// 视频占位图
const mainVideoPlaceholder =
  "https://img0.baidu.com/it/u=1258240022,1656450942&fm=253&fmt=auto&app=138&f=JPEG?w=667&h=500";
const subVideoPlaceholder =
  "https://img0.baidu.com/it/u=202967768,2443417331&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=667";

// 池子数据 - 固定数据，只有特定池子异常
const currentPoolId = ref(1);

// 定义异常池子的ID
const abnormalPools = [3, 7, 12, 15]; // 这些池子有异常

const pools = ref(
  Array.from({ length: 18 }, (_, i) => {
    const poolId = i + 1;
    const isAbnormal = abnormalPools.includes(poolId);
    return {
      id: poolId,
      status: isAbnormal ? "warning" : "normal",
      statusText: isAbnormal ? "异常" : "正常",
    };
  })
);

// 注释：报警数据已移至AlarmTable组件中

// 选择池子
const selectPool = (poolId: number, isManual: boolean = false) => {
  currentPoolId.value = poolId;
  // 更新环境监测数据
  updateEnvironmentData(poolId);
  // 更新视频监控
  updateVideoMonitor(poolId);
  // 更新图表数据会通过computed自动更新

  // 如果是手动点击，暂停轮询10秒后恢复
  if (isManual) {
    stopPoolRotation();
    setTimeout(() => {
      startPoolRotation();
    }, 10000); // 10秒后恢复自动轮询
  }
};

// 固定的环境数据映射
const environmentDataMap: Record<
  number,
  Array<{ name: string; value: string; unit: string }>
> = {
  3: [
    // 异常池子
    { name: "温度", value: "32.1", unit: "°C" }, // 异常 - 温度过高
    { name: "水位", value: "0.8", unit: "m" }, // 异常 - 水位过低
    { name: "PH", value: "8.5", unit: "" }, // 异常 - pH过高
    { name: "溶氧", value: "4.2", unit: "mg/L" }, // 异常 - 溶氧不足
    { name: "盐度", value: "18.8", unit: "‰" },
  ],
  7: [
    // 异常池子
    { name: "温度", value: "31.5", unit: "°C" }, // 异常 - 温度过高
    { name: "水位", value: "1.1", unit: "m" },
    { name: "PH", value: "6.2", unit: "" }, // 异常 - pH过低
    { name: "溶氧", value: "4.8", unit: "mg/L" }, // 异常 - 溶氧不足
    { name: "盐度", value: "16.2", unit: "‰" },
  ],
  12: [
    // 异常池子
    { name: "温度", value: "30.8", unit: "°C" }, // 异常 - 温度偏高
    { name: "水位", value: "0.9", unit: "m" }, // 异常 - 水位偏低
    { name: "PH", value: "8.2", unit: "" }, // 异常 - pH偏高
    { name: "溶氧", value: "5.1", unit: "mg/L" }, // 异常 - 溶氧偏低
    { name: "盐度", value: "17.5", unit: "‰" },
  ],
  15: [
    // 异常池子
    { name: "温度", value: "31.2", unit: "°C" }, // 异常 - 温度过高
    { name: "水位", value: "0.7", unit: "m" }, // 异常 - 水位过低
    { name: "PH", value: "6.8", unit: "" },
    { name: "溶氧", value: "4.5", unit: "mg/L" }, // 异常 - 溶氧不足
    { name: "盐度", value: "19.1", unit: "‰" }, // 异常 - 盐度过高
  ],
};

// 更新环境监测数据
const updateEnvironmentData = (poolId: number) => {
  // 使用固定数据，如果没有特定数据则使用默认正常数据
  if (environmentDataMap[poolId]) {
    environmentData.value = environmentDataMap[poolId];
  } else {
    // 默认正常数据
    environmentData.value = [
      {
        name: "温度",
        value: (28.0 + (poolId % 3) * 0.3).toFixed(1),
        unit: "°C",
      },
      { name: "水位", value: (1.2 + (poolId % 2) * 0.1).toFixed(1), unit: "m" },
      { name: "PH", value: (7.6 + (poolId % 4) * 0.1).toFixed(1), unit: "" },
      {
        name: "溶氧",
        value: (6.5 + (poolId % 3) * 0.2).toFixed(1),
        unit: "mg/L",
      },
      {
        name: "盐度",
        value: (15.0 + (poolId % 5) * 0.3).toFixed(1),
        unit: "‰",
      },
    ];
  }
};

// 更新视频监控
const updateVideoMonitor = (poolId: number) => {
  // 这里可以根据池子ID更新不同的监控画面
  // 实际项目中会连接真实的监控系统
  console.log(`切换到池子 ${poolId} 的监控画面`);
};

// 生成12小时整点数据
const generate12HourData = (
  poolId: number = 1,
  dataType: string = "default"
) => {
  const hours = [];
  const data = [];

  // 获取当前时间，并设置为整点
  const now = dayjs();
  const currentHour = now.hour();

  for (let i = 11; i >= 0; i--) {
    // 计算整点时间
    const hourTime = dayjs()
      .hour(currentHour)
      .minute(0)
      .second(0)
      .subtract(i, "hour");
    hours.push(hourTime.format("HH:00"));

    // 根据池子ID和数据类型生成不同的基础值
    let baseValue = 80;
    let variation = 20;

    switch (dataType) {
      case "liquid":
        baseValue = 90 + (poolId % 5) * 2;
        variation = 15;
        break;
      case "temperature":
        baseValue = 28 + (poolId % 3) * 1;
        variation = 5;
        break;
      case "ph":
        baseValue = 7.5 + (poolId % 4) * 0.1;
        variation = 1;
        break;
      default:
        baseValue = 80 + (poolId % 6) * 3;
        variation = 20;
    }

    // 添加时间趋势和随机波动
    const timeEffect = Math.sin((i / 24) * Math.PI * 2) * (variation * 0.3);
    const randomEffect = (Math.random() - 0.5) * variation * 0.4;
    const poolEffect = (poolId % 7) * 0.5;

    data.push(
      Number((baseValue + timeEffect + randomEffect + poolEffect).toFixed(2))
    );
  }
  return { hours, data };
};

// 液位监测图表配置
const liquidLevelOption = computed<EChartsOption>(() => {
  const { hours, data } = generate12HourData(currentPoolId.value, "liquid");
  return {
    backgroundColor: "transparent",
    grid: { left: 40, right: 20, top: 20, bottom: 20 },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#00ff88",
      borderWidth: 1,
      textStyle: {
        color: "#00ff88",
        fontSize: 12,
      },
      formatter: (params: unknown) => {
        const paramArray = Array.isArray(params) ? params : [params];
        const param = paramArray[0] as TooltipParam;
        return `${param.axisValue}<br/>液位: ${param.value}%`;
      },
    },
    xAxis: {
      type: "category",
      data: hours,
      axisLine: { lineStyle: { color: "#0efcff" } },
      axisLabel: {
        color: "#0efcff",
        fontSize: 10,
        interval: 0, // 显示所有标签
        margin: 8, // 标签与轴的距离
      },
    },
    yAxis: {
      type: "value",
      axisLine: { lineStyle: { color: "#0efcff" } },
      axisLabel: { color: "#0efcff", fontSize: 10 },
      splitLine: { lineStyle: { color: "rgba(14, 252, 255, 0.2)" } },
    },
    series: [
      {
        type: "line",
        data: data,
        smooth: true,
        lineStyle: { color: "#00ff88", width: 2 },
        itemStyle: { color: "#00ff88" },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: "rgba(0, 255, 136, 0.3)" },
              { offset: 1, color: "rgba(0, 255, 136, 0.05)" },
            ],
          },
        },
      },
    ],
  };
});

// 水温监测图表配置
const temperatureOption = computed<EChartsOption>(() => {
  const { hours, data } = generate12HourData(
    currentPoolId.value,
    "temperature"
  );
  return {
    backgroundColor: "transparent",
    grid: { left: 40, right: 20, top: 20, bottom: 20 },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#ff6b6b",
      borderWidth: 1,
      textStyle: {
        color: "#ff6b6b",
        fontSize: 12,
      },
      formatter: (params: unknown) => {
        const paramArray = Array.isArray(params) ? params : [params];
        const param = paramArray[0] as TooltipParam;
        return `${param.axisValue}<br/>水温: ${param.value}°C`;
      },
    },
    xAxis: {
      type: "category",
      data: hours,
      axisLine: { lineStyle: { color: "#0efcff" } },
      axisLabel: {
        color: "#0efcff",
        fontSize: 10,
        interval: 0, // 显示所有标签
        margin: 8, // 标签与轴的距离
      },
    },
    yAxis: {
      type: "value",
      axisLine: { lineStyle: { color: "#0efcff" } },
      axisLabel: { color: "#0efcff", fontSize: 10 },
      splitLine: { lineStyle: { color: "rgba(14, 252, 255, 0.2)" } },
    },
    series: [
      {
        type: "line",
        data: data,
        smooth: true,
        lineStyle: { color: "#ff6b6b", width: 2 },
        itemStyle: { color: "#ff6b6b" },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: "rgba(255, 107, 107, 0.3)" },
              { offset: 1, color: "rgba(255, 107, 107, 0.05)" },
            ],
          },
        },
      },
    ],
  };
});

// pH值监测图表配置
const phOption = computed<EChartsOption>(() => {
  const { hours, data } = generate12HourData(currentPoolId.value, "ph");
  return {
    backgroundColor: "transparent",
    grid: { left: 40, right: 20, top: 20, bottom: 20 },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#ffd93d",
      borderWidth: 1,
      textStyle: {
        color: "#ffd93d",
        fontSize: 12,
      },
      formatter: (params: unknown) => {
        const paramArray = Array.isArray(params) ? params : [params];
        const param = paramArray[0] as TooltipParam;
        return `${param.axisValue}<br/>pH值: ${param.value}`;
      },
    },
    xAxis: {
      type: "category",
      data: hours,
      axisLine: { lineStyle: { color: "#0efcff" } },
      axisLabel: {
        color: "#0efcff",
        fontSize: 10,
        interval: 0, // 显示所有标签
        margin: 8, // 标签与轴的距离
      },
    },
    yAxis: {
      type: "value",
      min: 6,
      max: 12,
      axisLine: { lineStyle: { color: "#0efcff" } },
      axisLabel: { color: "#0efcff", fontSize: 10 },
      splitLine: { lineStyle: { color: "rgba(14, 252, 255, 0.2)" } },
    },
    series: [
      {
        type: "line",
        data: data,
        smooth: true,
        lineStyle: { color: "#ffd93d", width: 2 },
        itemStyle: { color: "#ffd93d" },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: "rgba(255, 217, 61, 0.3)" },
              { offset: 1, color: "rgba(255, 217, 61, 0.05)" },
            ],
          },
        },
      },
    ],
  };
});

// 池子轮询定时器
let poolTimer: number | null = null;

// 开始池子轮询
const startPoolRotation = () => {
  poolTimer = setInterval(() => {
    // 自动切换到下一个池子
    const nextPoolId = currentPoolId.value >= 18 ? 1 : currentPoolId.value + 1;
    selectPool(nextPoolId);
  }, 5000); // 每5秒切换一次
};

// 停止池子轮询
const stopPoolRotation = () => {
  if (poolTimer) {
    clearInterval(poolTimer);
    poolTimer = null;
  }
};

// 生成所有区域数据（为池子数据计算提供基础数据）
const generateAllAreaData = () => {
  for (let i = 1; i <= 18; i++) {
    if (!areaData.value[i]) {
      areaData.value[i] = {
        small: Math.floor(Math.random() * 500) + 1000,
        medium: Math.floor(Math.random() * 300) + 700,
        large: Math.floor(Math.random() * 200) + 300,
      };
    }
  }
};

onMounted(() => {
  generateAllAreaData();
  // 启动池子轮询
  startPoolRotation();
});

onUnmounted(() => {
  // 停止池子轮询
  stopPoolRotation();
});
</script>

<style scoped>
.aquaculture-dashboard {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
  color: #66ccff;
  font-family: "Microsoft YaHei", sans-serif;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.aquaculture-dashboard::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 20%,
      rgba(102, 204, 255, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(0, 150, 255, 0.12) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 60%,
      rgba(51, 153, 255, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

/* 顶部标题栏 */
.header-section {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px;
  background: linear-gradient(
    90deg,
    rgba(14, 252, 255, 0.1) 0%,
    rgba(14, 252, 255, 0.05) 50%,
    rgba(14, 252, 255, 0.1) 100%
  );
  border-bottom: 2px solid rgba(14, 252, 255, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 10;
}

.header-section::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #0efcff 50%,
    transparent 100%
  );
  box-shadow: 0 0 10px #0efcff;
}

.seed-date {
  font-size: 16px;
  color: #0efcff;
  text-shadow: 0 0 10px rgba(14, 252, 255, 0.8);
}

.main-title {
  font-size: 32px;
  font-weight: bold;
  color: #0efcff;
  text-shadow: 0 0 20px rgba(14, 252, 255, 0.8);
  letter-spacing: 2px;
  margin: 0;
}

/* 主内容区域 */
.main-content {
  height: 0;
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden; /* 防止整体滚动 */
}

.left-section,
.right-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%; /* 确保占满高度 */
}

.center-section {
  flex: 1.5;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 面板通用样式 */
.panel {
  background: linear-gradient(
    135deg,
    rgba(14, 252, 255, 0.1) 0%,
    rgba(14, 252, 255, 0.05) 100%
  );
  border: 1px solid rgba(14, 252, 255, 0.3);
  border-radius: 12px;
  padding: 10px;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #0efcff 50%,
    transparent 100%
  );
  box-shadow: 0 0 10px #0efcff;
}

.panel-title {
  font-size: 18px;
  font-weight: bold;
  color: #0efcff;
  margin: 0 0 10px 0;
  text-shadow: 0 0 10px rgba(14, 252, 255, 0.8);
  text-align: center;
  flex-shrink: 0; /* 标题不收缩 */
}

/* 环境监测 */
.monitor-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.monitor-item {
  background: linear-gradient(
    135deg,
    rgba(0, 255, 136, 0.1) 0%,
    rgba(0, 255, 136, 0.05) 100%
  );
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-radius: 8px;
  padding: 10px 7px; /* 增加上下内边距 */
  position: relative;
  overflow: hidden;
  text-align: center;
}

.monitor-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #00ff88 50%,
    transparent 100%
  );
  box-shadow: 0 0 8px #00ff88;
}

.monitor-label {
  color: #00ff88;
  margin-bottom: 8px;
}

.monitor-value {
  font-weight: bold;
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
}

.monitor-unit {
  font-size: 12px;
  color: rgba(0, 255, 136, 0.7);
}

/* 24小时监测数据 */
.monitoring-content {
  text-align: center;
  flex: 1; /* 占用剩余空间 */
  height: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.current-area {
  font-size: 20px;
  font-weight: bold;
  color: #ffd93d;
  text-shadow: 0 0 15px rgba(255, 217, 61, 0.8);
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.shrimp-data {
  display: flex;
  flex-direction: column;
  gap: 15px; /* 增加间距 */
  flex: 1; /* 占用剩余空间 */
}

.shrimp-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px; /* 增加内边距 */
  background: linear-gradient(
    90deg,
    rgba(255, 217, 61, 0.1) 0%,
    rgba(255, 217, 61, 0.05) 100%
  );
  border: 1px solid rgba(255, 217, 61, 0.3);
  border-radius: 6px;
}

.shrimp-label {
  font-size: 14px;
  color: #ffd93d;
}

.shrimp-count {
  font-size: 16px;
  font-weight: bold;
  color: #ffd93d;
  text-shadow: 0 0 8px rgba(255, 217, 61, 0.6);
}

/* 视频监控 */
.video-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1; /* 占用剩余空间 */
  height: 0;
}

.main-video {
  flex: 2;
  height: 0;
  border: 1px solid rgba(14, 252, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.sub-videos {
  flex: 1;
  height: 0;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.sub-video {
  border: 1px solid rgba(14, 252, 255, 0.3);
  border-radius: 6px;
  overflow: hidden;
}

.video-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.8) contrast(1.2);
}

/* 池子显示区域 */
.pools-display {
  flex: 2;
}

.pools-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 12px;
  flex: 1; /* 占用剩余空间 */
}

.pool-item {
  background: linear-gradient(
    135deg,
    rgba(14, 252, 255, 0.1) 0%,
    rgba(14, 252, 255, 0.05) 100%
  );
  border: 2px solid rgba(14, 252, 255, 0.3);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.pool-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(14, 252, 255, 0.1) 50%,
    transparent 70%
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.pool-item:hover::before {
  transform: translateX(100%);
}

.pool-item:hover {
  border-color: #0efcff;
  box-shadow: 0 0 20px rgba(14, 252, 255, 0.5);
  transform: scale(1.05);
}

.pool-item.active {
  background: linear-gradient(
    135deg,
    rgba(0, 255, 136, 0.2) 0%,
    rgba(0, 255, 136, 0.1) 100%
  );
  border-color: #00ff88;
  box-shadow: 0 0 25px rgba(0, 255, 136, 0.6);
  animation: activePool 2s infinite;
}

@keyframes activePool {
  0%,
  100% {
    box-shadow: 0 0 25px rgba(0, 255, 136, 0.6);
  }
  50% {
    box-shadow: 0 0 35px rgba(0, 255, 136, 0.8);
  }
}

.pool-number {
  font-size: 18px;
  font-weight: bold;
  color: #0efcff;
  text-shadow: 0 0 10px rgba(14, 252, 255, 0.8);
  margin-bottom: 4px;
}

.pool-item.active .pool-number {
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
}

.pool-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  text-shadow: none;
}

.pool-status.normal {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
  border: 1px solid rgba(0, 255, 136, 0.5);
}

.pool-status.warning {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.5);
  animation: warning 1.5s infinite;
}

@keyframes warning {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 报警信息表格 */
.alarm-info {
  flex: 1;
}

.alarm-table-wrapper {
  flex: 1; /* 占用剩余空间 */
  display: flex;
  flex-direction: column;
}

/* 图表面板 */
.chart-panel {
  flex: 1;
}

.chart-container {
  flex: 1; /* 占用剩余空间 */
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.panel {
  animation: fadeInUp 0.6s ease-out;
}

.panel:nth-child(1) {
  animation-delay: 0.1s;
}
.panel:nth-child(2) {
  animation-delay: 0.2s;
}
.panel:nth-child(3) {
  animation-delay: 0.3s;
}

/* 数据更新动画 */
@keyframes dataUpdate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.monitor-value {
  transition: all 0.3s ease;
}

.monitor-value:hover {
  animation: dataUpdate 0.6s ease;
}

/* 图表容器动画 */
.chart-container {
  transition: all 0.3s ease;
}

.chart-panel:hover .chart-container {
  transform: scale(1.02);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(14, 252, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #0efcff, #00ff88);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #00ff88, #0efcff);
}
</style>
