<template>
  <div class="system-container">
    <div class="system-header">
      <h1 class="system-title">水产养殖全生命周期溯源系统</h1>
      <p class="system-subtitle">Aquaculture Full Life Cycle Traceability System</p>
    </div>
    
    <div class="system-content">
      <div class="placeholder-content">
        <div class="placeholder-icon">
          <div class="i-mdi-timeline text-6xl"></div>
        </div>
        <h2>水产养殖全生命周期溯源系统</h2>
        <p>系统正在开发中，敬请期待...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 水产养殖全生命周期溯源系统组件
</script>

<style scoped>
.system-container {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.system-header {
  padding: 30px 40px;
  border-bottom: 2px solid rgba(102, 204, 255, 0.3);
  background: rgba(102, 204, 255, 0.05);
}

.system-title {
  font-size: 32px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 20px rgba(102, 204, 255, 0.8);
  margin-bottom: 8px;
  letter-spacing: 2px;
}

.system-subtitle {
  font-size: 16px;
  color: rgba(102, 204, 255, 0.7);
  letter-spacing: 1px;
  margin: 0;
}

.system-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.placeholder-content {
  text-align: center;
  color: rgba(102, 204, 255, 0.8);
}

.placeholder-icon {
  margin-bottom: 30px;
  color: rgba(102, 204, 255, 0.6);
}

.placeholder-content h2 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #66ccff;
  text-shadow: 0 0 15px rgba(102, 204, 255, 0.6);
}

.placeholder-content p {
  font-size: 18px;
  color: rgba(102, 204, 255, 0.7);
  letter-spacing: 1px;
}
</style>
