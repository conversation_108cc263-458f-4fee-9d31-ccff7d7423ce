<template>
  <div class="market-table-container h-full flex flex-col">
    <!-- 表格头部 -->
    <div class="table-header border-b border-#0efcff/30">
      <div class="table-row px-4 py-3">
        <div class="table-grid">
          <div class="col-market font-bold">市场名称</div>
          <div class="col-spec font-bold">规格</div>
          <div class="col-price font-bold">价格</div>
          <div class="col-change font-bold">涨跌幅</div>
        </div>
      </div>
    </div>

    <!-- 表格内容 -->
    <div class="table-body flex-1 overflow-hidden">
      <div
        class="table-content"
        :style="{ transform: `translateY(-${currentOffset}px)` }"
      >
        <div
          v-for="(item, index) in displayData"
          :key="`${index}-${item.marketName}`"
          class="table-row border-b border-#0efcff/10 px-4 py-3 transition-colors hover:bg-#0efcff/5"
          :class="{ 'bg-#0efcff/3': index % 2 === 0 }"
        >
          <div class="table-grid">
            <div class="col-market truncate">
              {{ item.marketName }}
            </div>
            <div class="col-spec">{{ item.specification }}</div>
            <div class="col-price text-#e0f836 font-bold">
              {{ item.price }}
            </div>
            <div
              class="col-change font-bold"
              :class="getChangeColor(item.change)"
            >
              {{ formatChange(item.change) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";

interface MarketItem {
  marketName: string;
  specification: string;
  price: number;
  change: number;
}

// 模拟市场数据
const marketData = ref<MarketItem[]>([
  {
    marketName: "农副产品批发市场",
    specification: "小规格(80只/公斤)",
    price: 58.1,
    change: 3.5,
  },
  {
    marketName: "农副产品批发市场",
    specification: "中规格(60只/公斤)",
    price: 68.7,
    change: -1.2,
  },
  {
    marketName: "农副产品批发市场",
    specification: "大规格(40只/公斤)",
    price: 78.3,
    change: 2.1,
  },
  {
    marketName: "水产批发市场",
    specification: "小规格(80只/公斤)",
    price: 59.7,
    change: 1.6,
  },
  {
    marketName: "水产批发市场",
    specification: "中规格(60只/公斤)",
    price: 69.1,
    change: -0.5,
  },
  {
    marketName: "水产批发市场",
    specification: "大规格(40只/公斤)",
    price: 77.9,
    change: 2.8,
  },
]);

const currentOffset = ref(0);
const rowHeight = 52; // 每行的高度（包括padding和border）
const containerHeight = ref(0);
const visibleRows = ref(0);
const scrollSpeed = ref(0.5); // 每次滚动的像素数，控制滚动速度
let animationId: number | null = null;

// 计算可见行数
const calculateVisibleRows = () => {
  const container = document.querySelector(".table-body");
  if (container) {
    containerHeight.value = container.clientHeight;
    visibleRows.value = Math.floor(containerHeight.value / rowHeight);
  }
};

// 创建循环数据用于无缝滚动
const displayData = computed(() => {
  // 复制数据以实现无缝循环
  return [...marketData.value, ...marketData.value];
});

// 自动滚动函数
const autoScroll = () => {
  if (visibleRows.value === 0) {
    animationId = requestAnimationFrame(autoScroll);
    return;
  }

  // 计算一个完整数据集的高度
  const singleSetHeight = marketData.value.length * rowHeight;

  // 平滑滚动
  currentOffset.value += scrollSpeed.value;

  // 当滚动超过一个完整数据集时，重置到开始位置
  if (currentOffset.value >= singleSetHeight) {
    currentOffset.value = 0;
  }

  // 继续下一帧动画
  animationId = requestAnimationFrame(autoScroll);
};

// 格式化涨跌幅
const formatChange = (change: number): string => {
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(1)}%`;
};

// 获取涨跌幅颜色
const getChangeColor = (change: number): string => {
  if (change > 0) return "text-#ff4757"; // 红色表示上涨
  if (change < 0) return "text-#2ed573"; // 绿色表示下跌
  return "text-#0efcff"; // 蓝色表示持平
};

onMounted(() => {
  // 延迟计算，确保DOM已渲染
  setTimeout(() => {
    calculateVisibleRows();
    // 开始自动滚动动画
    animationId = requestAnimationFrame(autoScroll);
  }, 100);

  // 监听窗口大小变化
  window.addEventListener("resize", calculateVisibleRows);
});

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId);
  }
  window.removeEventListener("resize", calculateVisibleRows);
});
</script>

<style scoped>
.market-table-container {
  font-size: 13px;
  color: #0efcff;
}

.table-row {
  min-height: 52px;
  display: flex;
  align-items: center;
}

.table-header {
  background: rgba(14, 252, 255, 0.15);
  backdrop-filter: blur(5px);
  border-radius: 4px 4px 0 0;
  text-shadow: 0 0 8px rgba(14, 252, 255, 0.6);
}

.table-header .table-row {
  background: transparent;
}

.table-row:hover {
  background: rgba(102, 204, 255, 0.1) !important;
  box-shadow: inset 0 0 20px rgba(102, 204, 255, 0.12);
}

/* 表格网格布局 */
.table-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 1rem;
  align-items: center;
  width: 100%;
}

.col-market {
  white-space: wrap;
  text-align: center;
  justify-self: center;
}

.col-spec,
.col-price,
.col-change {
  text-align: center;
  justify-self: center;
}

/* 发光效果 */
.table-header div {
  text-shadow: 0 0 12px rgba(102, 204, 255, 0.9);
}

/* 价格数字发光 */
.text-#e0f836 {
  text-shadow: 0 0 8px rgba(224, 248, 54, 0.6);
}

/* 涨跌幅颜色发光效果 */
.text-#ff4757 {
  text-shadow: 0 0 8px rgba(255, 71, 87, 0.6);
}

.text-#2ed573 {
  text-shadow: 0 0 8px rgba(46, 213, 115, 0.6);
}
</style>
