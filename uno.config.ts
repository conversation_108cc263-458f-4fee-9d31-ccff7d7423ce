import { defineConfig, presetIcons, presetWind3 } from "unocss";

export default defineConfig({
  presets: [
    presetWind3(),
    presetIcons({
      collections: {
        // 使用常用的图标集合
        mdi: () =>
          import("@iconify-json/mdi/icons.json").then((i) => i.default),
      },
      extraProperties: {
        display: "inline-block",
        "vertical-align": "middle",
      },
    }),
  ],
  theme: {
    colors: {
      // 海洋蓝色主题配色
      ocean: {
        50: "#e3f2fd",
        100: "#bbdefb",
        200: "#90caf9",
        300: "#64b5f6",
        400: "#42a5f5",
        500: "#2196f3",
        600: "#1e88e5",
        700: "#1976d2",
        800: "#1565c0",
        900: "#0d47a1",
        primary: "#66ccff",
        secondary: "#4fc3f7",
        accent: "#29b6f6",
        light: "#81d4fa",
        dark: "#039be5",
        deep: "#0277bd",
      },
      // 海洋渐变色
      "ocean-gradient": {
        start: "#001122",
        middle: "#003366",
        end: "#004488",
      },
      // 海洋辅助色
      "ocean-support": {
        cyan: "#00bcd4",
        teal: "#009688",
        lightblue: "#03a9f4",
        indigo: "#3f51b5",
      },
    },
  },
});
